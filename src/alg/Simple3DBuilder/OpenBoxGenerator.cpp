#include <iostream>
#include <getopt.h>
#include <string>
#include <stdexcept>
#include "Simple3DBuilder.h"

void print_usage() {
    std::cout << "用法: ./OpenBoxGenerator [options]\n"
              << "选项:\n"
              << "  -x, --inner-length <value>   内框长度，单位毫米 (必填)\n"
              << "  -y, --inner-width <value>    内框宽度，单位毫米 (必填)\n"
              << "  -z, --inner-height <value>   内框高度，单位毫米 (必填)\n"
              << "  -l, --outer-length <value>   外框长度，单位毫米 (必填)\n"
              << "  -w, --outer-width <value>    外框宽度，单位毫米 (必填)\n"
              << "  -h, --outer-height <value>   外框高度，单位毫米 (必填)\n"
              << "  -c, --corner-radius <value>  倒角半径，单位毫米 (可选，默认: 没有倒角，即直角)\n"
              << "  -n, --cloud-points <value>   PLY点云数量，即STL模型采样点个数 (可选 默认: 100000)\n"
              << "  -o, --output-dir <path>      模型文件输出目录 (可选, 默认: 当前工作目录)\n";
}

int main(int argc, char* argv[]) {
    double inner_length = 0, inner_width = 0, inner_height = 0;
    double outer_length = 0, outer_width = 0, outer_height = 0;
    double corner_radius = 0;
    size_t cloud_points = 100000;
    std::string output_dir = ".";
    bool output_stl = false;

    const struct option long_options[] = {
        {"inner-length", required_argument, nullptr, 'x'},
        {"inner-width", required_argument, nullptr, 'y'},
        {"inner-height", required_argument, nullptr, 'z'},
        {"outer-length", required_argument, nullptr, 'l'},
        {"outer-width", required_argument, nullptr, 'w'},
        {"outer-height", required_argument, nullptr, 'h'},
        {"corner-radius", optional_argument, nullptr, 'c'},
        {"cloud-points", optional_argument, nullptr, 'n'},
        {"output-dir", optional_argument, nullptr, 'o'},
        {"output-stl", optional_argument, nullptr, 's'},
        {nullptr, 0, nullptr, 0}
    };

    int opt;
    while ((opt = getopt_long(argc, argv, "x:y:z:l:w:h:c:n:o:s:", long_options, nullptr)) != -1) {
        try {
            switch (opt) {
                case 'x': inner_length = std::stod(optarg); break;
                case 'y': inner_width = std::stod(optarg); break;
                case 'z': inner_height = std::stod(optarg); break;
                case 'l': outer_length = std::stod(optarg); break;
                case 'w': outer_width = std::stod(optarg); break;
                case 'h': outer_height = std::stod(optarg); break;
                case 'c': corner_radius = std::stod(optarg); break;
                case 'n': cloud_points = std::stoi(optarg); break;
                case 'o': output_dir = optarg; break;
                case 's': output_stl = std::stoi(optarg); break;
                default:
                    print_usage();
                    return 1;
            }
        } catch (const std::invalid_argument&) {
            std::cerr << "参数值无效，请输入数字。" << std::endl;
            return 1;
        }
    }

    if (inner_length <= 0 || inner_width <= 0 || inner_height <= 0 || outer_length <= 0 || outer_width <= 0 || outer_height <= 0 || cloud_points <= 0) {
        print_usage();
        return 1;
    }

    try {
        auto file = Simple3DBuilder::generate_openbox(inner_length, inner_width, inner_height, outer_length, outer_width, outer_height, corner_radius, cloud_points, output_dir, output_stl);
    } catch (const std::exception& e) {
        std::cerr << "错误：" << e.what() << std::endl;
        return 1;
    }

    return 0;
}
